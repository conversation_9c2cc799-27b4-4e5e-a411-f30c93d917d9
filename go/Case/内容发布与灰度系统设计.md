### 系统设计题："腾讯视频内容发布与灰度系统"设计

**面试官**：你好，我们来聊一个系统设计的题目。腾讯视频每天都会上线大量新的电影、电视剧、综艺等内容。为了确保新内容的上线和相关功能变更（比如某个电视剧启用了新的互动特性）不影响用户体验和系统稳定性，我们需要一个强大、灵活、可靠的"内容发布与灰度系统"。请你设计一下这个系统，需要考虑千万级DAU（日活跃用户）下的高可用和高可靠性。

---

好的，这是一个非常核心且有挑战的系统设计。它不仅是简单地"上线一个视频"，而是关系到整个平台稳定性和用户体验的命脉。我的设计思路如下：

#### a. 整体架构 (Overall Architecture)

这是一个典型的"控制面"与"数据面"分离的架构。控制面负责发布流程的编排和管理，数据面负责在用户请求时执行具体的灰度策略。

```mermaid
graph TD
    subgraph operator["操作员/平台"]
        SystemUI1["发布管理后台UI"]
    end

    subgraph kontrol-plane["控制平面"]
        API[控制API网关]
        Engine{"发布流程引擎"}
        DB((MySQL))
        Engine -.->|"1. 创建发布流程"| DB
        API --> Engine
    end

    subgraph config-center["配置中心"]
        Config[(Apollo/Nacos)]
    end

    subgraph monitoring["监控系统"]
        Monitor(Prometheus+Grafana)
        Alert(AlertManager)
    end

    subgraph data-plane["数据平面"]
        Gateway["API网关"]
        BFF["BFF层服务"]
        ClientApp["客户端App"]
    end

    SystemUI1 -->|"1. 操作员创建发布单"| API
    Engine -- >|"2. 更新流程将规则写入"| Config
    ClientApp -->|"4a. 可选：拉取规则"
    Gateway -..->|"3. 服务端预热/拉取规则"| Config
    Gateway -->|"4b. 代理并决策"| BFF
    ClientApp --> Gateway

    BFF -..->|业务数据上报| Monitor
    Alert -- >|"6. 监控到异常，告警或触发回滚"| Engine
    Monitor --> Alert
    Engine -..->|"5. 自动回滚"| Config

```

1.  **发布管理平台 (Control Plane UI)**: 面向运营和开发人员的Web界面，用于创建发布单、配置灰度规则（如灰度用户比例、用户画像标签、区域等）、设置发布时间、审批流程以及实时监控发布状态。
2.  **发布引擎 (Publishing Engine)**: 核心控制服务。它是一个状态机，负责执行发布单的整个生命周期（待发布 -> 灰度中 -> 全量 -> 已完成/已回滚）。它接收来自UI的指令，在预定时间开始或调整灰度策略，并将最终的灰度规则推送到配置中心。
3.  **配置中心 (Configuration Center)**: 所有灰度规则的唯一可信源（Single Source of Truth）。技术选型上可以是Apollo或Nacos。它存储着哪个内容ID、哪个功能正在被灰度，以及具体的规则是什么。它具备近实时的配置推送能力。
4.  **数据面/执行层 (Data Plane)**:
    *   **服务端执行**: 在API网关或BFF (Backend for Frontend) 层，服务会监听配置中心的变化。当用户请求内容信息时，网关/BFF会根据从配置中心拉取的规则，结合用户的属性（UserID, IP, DeviceID等），实时计算该用户是否命中灰度。如果命中，则返回新版本的内容信息（如新的播放地址、新的UI元素定义等）。
    *   **客户端执行**: App也可以直接订阅配置中心的规则，在客户端本地进行决策。通常用于纯前端表现相关的灰度，如UI布局变更。
5.  **监控与告警系统 (Monitoring & Alerting)**: 该系统订阅所有发布事件，并实时采集发布组（灰度用户）和对照组（非灰度用户）的核心业务指标（如播放成功率、卡顿率、完播率）和系统指标（错误码、延迟）。一旦发布组的指标出现显著下跌，立即触发告警甚至自动回滚。

#### b. 核心设计：灰度策略与流量切分

这是系统的灵魂，必须做到既灵活又高效。

1.  **用户分桶与流量划分**:
    *   **分层抽样**: 我们不能把所有灰度实验都放在一个"桶"里。应该设计一个分层模型，比如流量先进A层实验（如视频编码格式变更），出来的流量再进B层实验（如推荐算法变更）。这样可以保证实验的正交性。
    *   **精细化规则**: 规则引擎必须支持多种维度的组合：
        *   **用户百分比**: `hash(user_id) % 100 < 1`，将1%的用户划入灰度。这是最基本的方式，可以保证同一个用户每次都被划入同一个桶。
        *   **用户画像标签**: `tags.contains("vip_level > 5")` 或 `tags.contains("region:beijing")`，面向特定高价值用户或区域发布。
        *   **设备/版本信息**: `os == "Android" && app_version > "8.5.0"`，面向特定客户端版本。
        *   **白名单**: `user_id in (internal_staff_list)`，用于内部测试。
    *   **规则引擎选型**: 可以使用开源的表达式引擎如`Aviator`或`QLExpress`，它们能将复杂的布尔逻辑字符串解析并高效执行。

2.  **流量切分的实现**:
    *   **推荐服务端切分**: 决策逻辑放在API网关或BFF层。
        *   **优点**: 规则更新快（秒级生效），逻辑收敛，安全可控，对客户端透明。
        *   **缺点**: 对网关性能有一定要求。
    *   **实现方式**: 网关启动时从配置中心加载所有灰度规则，并监听变化。每个用户请求过来时，会经过一个"灰度决策中间件"，该中间件遍历所有"进行中"的灰度规则，判断当前用户是否命中。为了性能，规则可以在内存中编译成可执行体。

#### c. 存储与配置中心

1.  **配置中心**:
    *   **技术选型**: **Apollo**。
    *   **理由**: 具备完善的权限管理、版本发布和回滚、灰度发布（对配置本身的灰度）、多环境支持，并且有强大的客户端和近实时推送能力，非常适合这个场景。
    *   **数据模型**: 在Apollo中，每个发布单可以是一个`namespace`。
        *   `Key`: `release.content.c00123abcde`
        *   `Value` (JSON格式):
            ```json
            {
              "enabled": true,
              "rule": "hash(user_id) % 100 < 10", // 10%用户
              "metadata": { "playback_url": "new_url", "drm_key": "new_key" },
              "targetAudience": "tags.contains('region:shanghai')"
            }
            ```

2.  **发布状态存储**:
    *   **技术选型**: **MySQL**。
    *   **理由**: 发布单的创建、审批、执行历史是一个事务性很强的操作流程，需要ACID保证。MySQL完全能胜任。
    *   **核心表设计**:
        *   `release_plans`: 存储发布单的主信息（ID, 发布内容, 创建人, 状态, 审批信息）。
        *   `release_steps`: 记录一个发布单的每一步操作（如 "创建发布", "灰度到1%", "灰度到10%", "全量", "回滚"），形成操作历史。

#### d. 高可用与高可靠性

1.  **发布引擎高可用**: 发布引擎是无状态服务，可以水平扩展部署在K8s上。核心的状态都存储在MySQL和Apollo中。使用乐观锁来防止多个实例同时操作同一个发布单。
2.  **快速回滚机制**:
    *   **一键回滚**: 在发布管理平台提供"回滚"按钮。点击后，发布引擎会立即修改Apollo中的配置（如将`enabled`置为`false`），规则秒级失效。
    *   **自动熔断/回滚**: 监控系统与发布引擎联动。当监控发现灰度组的"播放失败率"相比对照组上涨20%时，可以调用发布引擎的回滚API，实现无人干预的风险控制。
3.  **紧急刹车 (Kill Switch)**: 必须有一个全局的"紧急刹车"开关。比如在Apollo里配置一个`global.release.disabled=false`的开关。所有数据面服务（网关、BFF）都强制依赖此开关，一旦设为`true`，则绕过所有灰度逻辑，所有用户都看到全量、稳定的版本。这是应对极端灾难的最后防线。
4.  **发布审批流**: 重要的发布（如热门大剧）必须经过至少两级审批（如开发负责人、测试负责人），防止误操作。

#### e. 监控与度量

监控是保障灰度发布成功的眼睛。

1.  **数据采集**: 客户端和服务端都需要上报详细的日志，日志必须包含用户所属的所有灰度实验组标签。
2.  **指标对比**: 核心是**同维度对比**。在监控平台（如Grafana）上，需要将灰度组和对照组的数据放在一张图里对比。
    *   **业务核心指标**: 播放成功率、播放卡顿率、用户平均观看时长。
    *   **系统健康指标**: API错误率 (HTTP 5xx)、API平均延迟。
3.  **A/B实验思想**: 本质上，每一次灰度发布都是一次严谨的A/B实验。除了监控负向指标外，也需要关注正向指标。比如，为某个体育内容上线了"多视角观看"的灰度功能，就需要关注"人均视角切换次数"、"总观看时长"等指标是否在灰度组有提升。 