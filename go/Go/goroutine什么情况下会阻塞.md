# Goroutine阻塞场景详解

## 主要阻塞场景

### 1. Channel操作阻塞
```go
// 无缓冲channel - 同步阻塞
ch := make(chan int)
ch <- 1  // 阻塞，直到有接收者

// 有缓冲channel - 缓冲区满时阻塞
ch := make(chan int, 2)
ch <- 1; ch <- 2; ch <- 3  // 第三个发送阻塞
```

### 2. 锁竞争阻塞
```go
var mu sync.Mutex
mu.Lock()
// 其他goroutine调用mu.Lock()会阻塞
mu.Unlock()
```

### 3. I/O操作阻塞
- **网络I/O**: HTTP请求、TCP连接
- **文件I/O**: 读写文件、目录操作
- **数据库I/O**: SQL查询、事务处理

### 4. 系统调用阻塞
- **阻塞系统调用**: 文件操作、网络操作
- **CGO调用**: 调用C语言库函数
- **外部依赖**: 等待外部服务响应

### 5. 同步原语阻塞
```go
// WaitGroup等待
var wg sync.WaitGroup
wg.Wait()  // 阻塞直到计数器为0

// Cond条件变量
cond.Wait()  // 阻塞直到收到信号
```

### 6. Select语句阻塞
```go
select {
case <-ch1:  // 如果所有case都阻塞
case <-ch2:  // 且没有default分支
}            // 则select阻塞
```

### 7. 时间相关阻塞
```go
time.Sleep(time.Second)     // 主动休眠
<-time.After(time.Second)   // 定时器阻塞
```

## 避免阻塞的策略

### 1. 使用缓冲Channel
```go
ch := make(chan int, 100)  // 缓冲区减少阻塞
```

### 2. 超时控制
```go
select {
case result := <-ch:
    return result
case <-time.After(5*time.Second):
    return errors.New("timeout")
}
```

### 3. 非阻塞操作
```go
select {
case ch <- data:
    // 发送成功
default:
    // 立即返回，不阻塞
}
```

### 4. Context取消
```go
select {
case <-ch:
    // 正常处理
case <-ctx.Done():
    return ctx.Err()  // 取消操作
}
```

## 面试要点
1. **Channel阻塞**: 无缓冲同步阻塞，有缓冲满时阻塞
2. **锁阻塞**: 互斥锁竞争、死锁检测
3. **I/O阻塞**: 网络、文件、数据库操作
4. **避免策略**: 超时、非阻塞、Context取消