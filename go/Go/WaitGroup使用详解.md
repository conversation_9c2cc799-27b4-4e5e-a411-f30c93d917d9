# WaitGroup面试要点

## 核心概念
WaitGroup用于等待一组goroutine完成执行，通过计数器机制实现同步。

### 三个关键方法
- **Add(delta int)**: 增加计数器
- **Done()**: 减少计数器(等价于Add(-1))
- **Wait()**: 阻塞直到计数器归零

### 基本用法
```go
var wg sync.WaitGroup

for i := 0; i < 5; i++ {
    wg.Add(1)  // 启动前增加计数
    go func(id int) {
        defer wg.Done()  // 确保调用Done
        doWork(id)
    }(i)
}

wg.Wait()  // 等待所有完成
```

## 常见错误

### 1. 忘记调用Done()
```go
// 错误：忘记Done()导致永久等待
wg.Add(1)
go func() {
    doWork()
    // 忘记wg.Done()
}()
wg.Wait()  // 永远不会返回

// 正确：使用defer确保调用
wg.Add(1)
go func() {
    defer wg.Done()  // 推荐用defer
    doWork()
}()
```

### 2. Add和Done不匹配
```go
// 错误：在goroutine内部Add
for i := 0; i < 5; i++ {
    go func() {
        wg.Add(1)  // 错误位置
        defer wg.Done()
        doWork()
    }()
}

// 正确：启动前Add
for i := 0; i < 5; i++ {
    wg.Add(1)  // 正确位置
    go func() {
        defer wg.Done()
        doWork()
    }()
}
```

### 3. 重复使用WaitGroup
- **问题**: WaitGroup不能重复使用
- **解决**: 每次使用新的WaitGroup实例

## 高级用法

### 1. 带超时等待
```go
func waitWithTimeout() error {
    var wg sync.WaitGroup
    done := make(chan struct{})

    // 启动任务
    wg.Add(5)
    for i := 0; i < 5; i++ {
        go func() {
            defer wg.Done()
            longTask()
        }()
    }

    // 异步等待
    go func() {
        wg.Wait()
        close(done)
    }()

    // 超时控制
    select {
    case <-done:
        return nil
    case <-time.After(10*time.Second):
        return errors.New("超时")
    }
}
```

### 2. 错误收集
```go
func waitWithErrors() error {
    var wg sync.WaitGroup
    errCh := make(chan error, 5)

    wg.Add(5)
    for i := 0; i < 5; i++ {
        go func() {
            defer wg.Done()
            if err := taskWithError(); err != nil {
                errCh <- err
            }
        }()
    }

    go func() {
        wg.Wait()
        close(errCh)
    }()

    // 收集所有错误
    for err := range errCh {
        if err != nil {
            return err  // 返回第一个错误
        }
    }
    return nil
}
```

## 与其他方案对比

### WaitGroup vs Channel
- **WaitGroup**: 适合等待一组任务完成，无需传递数据
- **Channel**: 适合需要传递结果或错误的场景

### WaitGroup vs Context
- **WaitGroup**: 等待任务完成
- **Context**: 取消和超时控制
- **组合使用**: Context控制取消，WaitGroup等待完成

## 面试要点

### 核心问题
1. **三个方法作用？**
   - Add: 增加计数器
   - Done: 减少计数器
   - Wait: 等待计数器归零

2. **常见错误？**
   - 忘记调用Done()
   - Add/Done不匹配
   - 重复使用同一个WaitGroup

3. **最佳实践？**
   - 使用defer Done()确保调用
   - 启动goroutine前调用Add
   - 每次使用新的WaitGroup实例

4. **适用场景？**
   - 等待一组goroutine完成
   - 批量任务处理
   - 服务优雅关闭

### 一句话总结
> WaitGroup通过计数器机制等待一组goroutine完成，是Go并发编程的基础同步原语
