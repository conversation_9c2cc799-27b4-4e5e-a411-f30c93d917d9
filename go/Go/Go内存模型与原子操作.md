# Go内存模型与原子操作面试要点

## Go内存模型基础

### 核心概念
- **内存模型**: 定义多goroutine间内存可见性规则
- **Happens-Before**: 确定事件先后顺序的关系
- **数据竞争**: 多个goroutine同时访问同一内存位置且至少一个是写操作

### Happens-Before规则
1. **单个goroutine内**: 程序按源代码顺序执行
2. **Goroutine创建**: 创建语句 happens-before goroutine开始执行
3. **Channel操作**: 发送 happens-before 对应的接收完成
4. **Mutex操作**: 第n次Unlock() happens-before 第m次Lock() (n<m)
5. **Once操作**: Do(f)中f()返回 happens-before 任何Do(f)调用返回

### 数据竞争示例
```go
// 错误：存在数据竞争
var counter int
for i := 0; i < 1000; i++ {
    go func() {
        counter++ // 竞争！结果不确定
    }()
}

// 正确：使用同步机制
var counter int64
for i := 0; i < 1000; i++ {
    go func() {
        atomic.AddInt64(&counter, 1) // 原子操作
    }()
}
```

## 原子操作详解

### 支持的操作类型
```go
import "sync/atomic"

// 基本操作
atomic.AddInt64(&counter, 1)        // 原子加法
atomic.LoadInt64(&counter)          // 原子读取
atomic.StoreInt64(&counter, 100)    // 原子存储
atomic.SwapInt64(&counter, 200)     // 原子交换
atomic.CompareAndSwapInt64(&counter, old, new) // CAS

// 指针操作
atomic.LoadPointer(&ptr)
atomic.StorePointer(&ptr, unsafe.Pointer(obj))
atomic.CompareAndSwapPointer(&ptr, old, new)

// 任意类型
var value atomic.Value
value.Store(obj)
obj := value.Load()
```

### 原子计数器示例
```go
type AtomicCounter struct {
    value int64
}

func (c *AtomicCounter) Increment() {
    atomic.AddInt64(&c.value, 1)
}

func (c *AtomicCounter) Value() int64 {
    return atomic.LoadInt64(&c.value)
}

func (c *AtomicCounter) CompareAndSwap(old, new int64) bool {
    return atomic.CompareAndSwapInt64(&c.value, old, new)
}
```

### 原子配置更新
```go
type AtomicConfig struct {
    config unsafe.Pointer
}

func (ac *AtomicConfig) Store(cfg *Config) {
    atomic.StorePointer(&ac.config, unsafe.Pointer(cfg))
}

func (ac *AtomicConfig) Load() *Config {
    return (*Config)(atomic.LoadPointer(&ac.config))
}
```

## 原子操作 vs 互斥锁

### 性能对比
- **原子操作**: 通常比互斥锁快2-10倍
- **互斥锁**: 有额外的调度开销

### 适用场景对比
| 场景 | 原子操作 | 互斥锁 |
|------|----------|--------|
| 简单数值操作 | ✅ 推荐 | ❌ 过重 |
| 复杂逻辑保护 | ❌ 不适用 | ✅ 推荐 |
| 多个变量同步 | ❌ 困难 | ✅ 简单 |
| 性能要求高 | ✅ 更快 | ❌ 较慢 |
| 代码可读性 | ❌ 复杂 | ✅ 清晰 |

## 常见问题和最佳实践

### ABA问题
```go
// ABA问题：值从A变为B再变回A，CAS无法检测到变化
// 解决方案：使用版本号或指针标记
type VersionedValue struct {
    value   int64
    version int64
}

func (vv *VersionedValue) CompareAndSwap(oldVal, newVal int64) bool {
    for {
        old := atomic.LoadInt64(&vv.value)
        oldVer := atomic.LoadInt64(&vv.version)

        if old != oldVal {
            return false
        }

        // 同时更新值和版本号
        if atomic.CompareAndSwapInt64(&vv.value, old, newVal) {
            atomic.AddInt64(&vv.version, 1)
            return true
        }
    }
}
```

### 内存对齐优化
```go
// 避免false sharing
type PaddedCounter struct {
    value int64
    _     [7]int64 // 填充到64字节（一个缓存行）
}
```

## 面试要点

### 核心问题
1. **什么是Go内存模型？**
   - 定义多goroutine间内存可见性规则
   - 通过happens-before关系确定事件顺序

2. **什么时候使用原子操作？**
   - 简单数值操作（计数器、标志位）
   - 性能要求极高的场景
   - 实现无锁数据结构

3. **原子操作的优势？**
   - 性能比互斥锁好
   - 避免死锁
   - 提供内存屏障保证

4. **原子操作的限制？**
   - 只支持简单操作
   - 复杂逻辑仍需要锁
   - 容易出现ABA等问题

### 最佳实践
- 优先使用高级同步原语（Channel、Mutex）
- 原子操作适用于简单数值操作
- 避免复杂的无锁算法
- 注意内存对齐，避免false sharing
- 使用竞态检测器：`go run -race`

### 一句话总结
> Go内存模型定义了多goroutine间的内存可见性规则，原子操作提供了高性能的无锁同步机制


