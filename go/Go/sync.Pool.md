# sync.Pool对象池

## 核心概念

sync.Pool是Go标准库提供的临时对象缓存池，用于减少内存分配和GC压力。

### 基本特性
- **并发安全**: 多goroutine可安全使用
- **临时存储**: 对象在GC时会被清理
- **自动扩缩**: 根据需要自动调整池大小

## 工作机制

### 核心方法
```go
type Pool struct {
    New func() interface{}  // 创建新对象的函数
}

// 从池中获取对象
obj := pool.Get()

// 将对象放回池中
pool.Put(obj)
```

### 内部实现
- **本地池**: 每个P维护独立的本地池，减少锁竞争
- **全局池**: 本地池为空时从全局池获取
- **GC清理**: 每次GC时清空池中所有对象

## 使用示例

### 基本用法
```go
var bufferPool = sync.Pool{
    New: func() interface{} {
        return make([]byte, 1024)
    },
}

// 获取buffer
buf := bufferPool.Get().([]byte)
defer bufferPool.Put(buf)

// 使用buffer
// ...
```

### 实际应用
```go
// HTTP响应缓存
var responsePool = sync.Pool{
    New: func() interface{} {
        return &Response{}
    },
}

func handleRequest() {
    resp := responsePool.Get().(*Response)
    defer responsePool.Put(resp)

    resp.Reset()  // 重置对象状态
    // 处理请求...
}
```

## 适用场景

### 适合使用
- **频繁创建销毁**: 对象创建成本高
- **短生命周期**: 对象使用时间短
- **高并发场景**: 减少GC压力

### 不适合使用
- **长期存储**: 对象需要长期保存
- **创建成本低**: 简单对象如int、string
- **状态复杂**: 对象状态难以重置

## 注意事项

### 对象重置
```go
type Buffer struct {
    data []byte
}

func (b *Buffer) Reset() {
    b.data = b.data[:0]  // 重置切片长度
}

// 使用时必须重置
buf := pool.Get().(*Buffer)
buf.Reset()  // 重要：清理之前的状态
defer pool.Put(buf)
```

### 内存泄漏风险
- **大对象**: 避免缓存过大的对象
- **引用清理**: 确保对象不持有其他资源的引用
- **及时归还**: 使用defer确保对象归还

## 面试要点

1. **工作原理**: 本地池+全局池，减少锁竞争
2. **GC影响**: 每次GC清空池，不能依赖对象持久存在
3. **性能优化**: 减少内存分配，降低GC压力
4. **使用场景**: 频繁创建的临时对象
5. **注意事项**: 对象重置、内存泄漏防范

### 一句话总结
> sync.Pool通过对象复用减少内存分配和GC压力，适用于频繁创建的临时对象缓存