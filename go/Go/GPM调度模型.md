# Go GPM调度模型

## 核心概念

GPM是Go运行时的调度模型，实现了用户态的协程调度：
- **G (Goroutine)**: 协程，轻量级线程
- **P (Processor)**: 处理器，调度上下文
- **M (Machine)**: 机器，系统线程

## GPM组件详解

### G (Goroutine)
- **特点**: 初始栈2KB，可扩展至1GB
- **状态**: _Gidle → _Grunnable → _Grunning → _Gdead
- **优势**: 创建成本低，可创建百万级协程

### P (Processor)
- **数量**: 由GOMAXPROCS决定，默认=CPU核数
- **功能**:
  - 维护本地运行队列(256个G)
  - 管理内存分配器
  - 执行垃圾回收

### M (Machine)
- **本质**: 系统线程的抽象
- **限制**: 默认最多10000个
- **状态**: 自旋(寻找工作) 或 阻塞(执行系统调用)

## 调度机制

### 调度时机
1. **主动调度**: `runtime.Gosched()`主动让出CPU
2. **被动调度**: channel操作、锁等待、系统调用
3. **抢占调度**: Go 1.14+支持异步抢占

### 调度流程
1. **本地队列**: 优先从P的本地队列获取G
2. **全局队列**: 本地队列空时检查全局队列
3. **工作窃取**: 从其他P窃取一半的G
4. **网络轮询**: 检查网络I/O就绪的G

### 工作窃取(Work Stealing)
- **触发条件**: P的本地队列为空
- **窃取策略**: 随机选择其他P，窃取一半G
- **优势**: 负载均衡，避免某些P空闲

## 系统调用处理

### 阻塞系统调用
当G执行阻塞系统调用时：
1. **M与P分离**: M进入系统调用，P被释放
2. **P寻找新M**: P寻找空闲M或创建新M继续调度
3. **M重新获取P**: 系统调用完成后，M尝试重新获取P

### 非阻塞系统调用
- **网络I/O**: 使用epoll/kqueue等，不阻塞M
- **文件I/O**: Go 1.9+支持非阻塞文件操作

## 性能优化特性

### 本地队列优化
- **减少锁竞争**: 每个P维护独立的本地队列
- **局部性原理**: 新G优先放入当前P的本地队列
- **负载均衡**: 本地队列满时，一半G移至全局队列

### 自旋机制
- **避免频繁休眠**: M在短时间内自旋寻找工作
- **快速响应**: 减少G调度延迟
- **资源控制**: 限制自旋M的数量

## GOMAXPROCS调优

### 设置原则
- **CPU密集型**: GOMAXPROCS = CPU核数
- **I/O密集型**: 可适当增加，但不宜过大
- **容器环境**: 根据CPU限制动态调整

### 影响因素
- **过小**: 无法充分利用多核CPU
- **过大**: 增加调度开销，上下文切换频繁
- **默认值**: 等于CPU核数，适合大多数场景

## 面试要点

### 核心问题
1. **GPM各组件作用？**
   - G: 协程，用户代码执行单元
   - P: 处理器，调度上下文和本地队列
   - M: 机器，系统线程抽象

2. **工作窃取机制？**
   - P本地队列空时从其他P窃取一半G
   - 实现负载均衡，提高CPU利用率

3. **系统调用处理？**
   - 阻塞调用时M与P分离
   - P寻找新M继续调度，避免阻塞

4. **GOMAXPROCS影响？**
   - 决定P的数量和并行度
   - 需要根据应用特性合理设置

### 关键优势
- **用户态调度**: 避免内核态切换开销
- **工作窃取**: 实现负载均衡
- **M:N模型**: 多个G映射到少量M
- **抢占调度**: 防止G长时间占用CPU

### 一句话总结
> GPM模型实现了高效的用户态协程调度，通过工作窃取和M:N映射支持大规模并发
